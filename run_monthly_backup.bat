@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Monthly Backup System                             S4NG-7
REM ===============================================================
REM Unified monthly backup system with integrated functionality:
REM - Monthly backup operations (by month/year)
REM - Historical backup operations (by date range)
REM - Multiple backup strategies for maximum reliability
REM
REM This script runs complete monthly or historical backups using
REM the existing daily backup system. It processes data
REM sequentially for maximum reliability.
REM
REM Usage:
REM   run_monthly_backup.bat [mode] [parameters] [options]
REM
REM Modes:
REM   monthly            Monthly backup by month/year (default)
REM   historical         Historical backup by date range
REM   help               Show this help message
REM
REM Monthly Mode Examples:
REM   run_monthly_backup.bat march 2025
REM   run_monthly_backup.bat 3 2025
REM   run_monthly_backup.bat monthly march 2024 --dry-run
REM   run_monthly_backup.bat 3 2025 --start-day 15
REM
REM Historical Mode Examples:
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-31
REM   run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
REM
REM Monthly Options:
REM   --start-day DD     Start from specific day (default: 1)
REM   --end-day DD       End at specific day (default: last day of month)
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM
REM Historical Options:
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM   --single-table     Test with single table only
REM ===============================================================

REM Set the current directory to the script directory
cd /d "%~dp0"

REM Set default values
set MODE=monthly
set MONTH_NAME=%1
set YEAR=%2
set START_DAY=1
set END_DAY=
set DRY_RUN=false
set VERBOSE=false
set SINGLE_TABLE=false
set PYTHON_CMD=python

REM SECURITY FIX: Use ConfigManager-based paths instead of hardcoded paths
REM Get configured paths using the security helper
for /f %%i in ('python utils\batch_security_helper.py get-path --path-type script_dir --default scripts') do set "SCRIPT_DIR=%%i"
for /f %%i in ('python utils\batch_security_helper.py get-path --path-type log_dir --default logs') do set "LOG_BASE_DIR=%%i"
for /f %%i in ('python utils\batch_security_helper.py get-path --path-type temp_dir --default temp') do set "TEMP_BASE_DIR=%%i"

REM Set script paths using configured directories
set HISTORICAL_BACKUP_SCRIPT=%SCRIPT_DIR%\historical_backup_processor.py

REM SECURITY FIX: Input validation before processing arguments
REM Validate that we have at least one argument and it's not malicious
if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="--help" goto help

REM SECURITY FIX: Basic input sanitization - check for dangerous characters
echo %1 | findstr /r "[;&|`$()<>*?]" >nul
if %ERRORLEVEL% EQU 0 (
    echo ERROR: Invalid characters detected in first parameter. Only alphanumeric characters, dots, and hyphens are allowed.
    exit /b 1
)

REM Process command line arguments and determine mode

REM Check if first argument is a mode
if "%1"=="monthly" (
    set MODE=monthly
    set MONTH_NAME=%2
    set YEAR=%3
    shift
    shift
    goto monthly_mode
)
if "%1"=="historical" (
    set MODE=historical
    set START_DATE=%2
    set END_DATE=%3
    shift
    shift
    goto historical_mode
)

REM SECURITY FIX: Enhanced date validation for historical mode
REM If first argument looks like a date (YYYY-MM-DD), validate it properly
echo %1 | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
if %ERRORLEVEL% EQU 0 (
    REM SECURITY FIX: Validate date format using TNGD validation patterns
    python utils\batch_security_helper.py validate-date --date %1 >nul 2>&1
    if !ERRORLEVEL! NEQ 0 (
        echo ERROR: Invalid start date format: %1
        echo Expected format: YYYY-MM-DD
        exit /b 1
    )

    REM Validate second date if provided
    if not "%2"=="" (
        python utils\batch_security_helper.py validate-date --date %2 >nul 2>&1
        if !ERRORLEVEL! NEQ 0 (
            echo ERROR: Invalid end date format: %2
            echo Expected format: YYYY-MM-DD
            exit /b 1
        )
    )

    set MODE=historical
    set START_DATE=%1
    set END_DATE=%2
    shift
    goto historical_mode
)

REM Default to monthly mode
set MODE=monthly
goto monthly_mode

:monthly_mode
REM SECURITY FIX: Enhanced month/year validation using security helper
REM Set default year if not provided
if "%YEAR%"=="" set YEAR=2025

REM SECURITY FIX: Validate month and year using TNGD validation patterns
for /f "tokens=1,2,3" %%a in ('python utils\batch_security_helper.py validate-month-year --month "%MONTH_NAME%" --year "%YEAR%" 2^>nul') do (
    if "%%a"=="VALID" (
        set MONTH_NUM=%%b
        set YEAR=%%c
    ) else (
        echo ERROR: Invalid month or year parameters
        echo Month: %MONTH_NAME%, Year: %YEAR%
        exit /b 1
    )
)

REM Check if validation failed (no output from helper)
if "%MONTH_NUM%"=="" (
    echo ERROR: Month/year validation failed
    echo Month: %MONTH_NAME%, Year: %YEAR%
    echo Valid months: january-december or 1-12
    echo Valid years: 2020-2030
    exit /b 1
)

REM Parse additional options
shift
shift
:parse_options
if "%1"=="" goto options_done
if "%1"=="--start-day" (
    set START_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--end-day" (
    set END_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_options
)
shift & goto parse_options

:options_done

REM SECURITY FIX: Determine end day using correct leap year calculation
if "%END_DAY%"=="" (
    REM SECURITY FIX: Use security helper for correct leap year calculation
    for /f %%d in ('python utils\batch_security_helper.py calculate-days --month %MONTH_NUM% --year %YEAR%') do set END_DAY=%%d

    REM Fallback to manual calculation if helper fails
    if "%END_DAY%"=="" (
        if %MONTH_NUM%==1 set END_DAY=31
        if %MONTH_NUM%==2 set END_DAY=28
        if %MONTH_NUM%==3 set END_DAY=31
        if %MONTH_NUM%==4 set END_DAY=30
        if %MONTH_NUM%==5 set END_DAY=31
        if %MONTH_NUM%==6 set END_DAY=30
        if %MONTH_NUM%==7 set END_DAY=31
        if %MONTH_NUM%==8 set END_DAY=31
        if %MONTH_NUM%==9 set END_DAY=30
        if %MONTH_NUM%==10 set END_DAY=31
        if %MONTH_NUM%==11 set END_DAY=30
        if %MONTH_NUM%==12 set END_DAY=31

        REM SECURITY FIX: Correct leap year calculation for February
        if %MONTH_NUM%==2 (
            REM Divisible by 400: leap year
            set /a leap_check_400=%YEAR% %% 400
            if !leap_check_400!==0 (
                set END_DAY=29
            ) else (
                REM Divisible by 100: not a leap year
                set /a leap_check_100=%YEAR% %% 100
                if !leap_check_100!==0 (
                    set END_DAY=28
                ) else (
                    REM Divisible by 4: leap year
                    set /a leap_check_4=%YEAR% %% 4
                    if !leap_check_4!==0 (
                        set END_DAY=29
                    ) else (
                        set END_DAY=28
                    )
                )
            )
        )
    )
)

echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Days: %START_DAY% to %END_DAY%
if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM SECURITY FIX: Create monthly log directory using configured paths
REM Format month number with leading zero
set MONTH_FORMATTED=%MONTH_NUM%
if %MONTH_NUM% LSS 10 set MONTH_FORMATTED=0%MONTH_NUM%

REM Use configured log directory
set LOG_DIR=%LOG_BASE_DIR%\monthly\%YEAR%-%MONTH_FORMATTED%
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Initialize counters
set SUCCESSFUL_DAYS=0
set FAILED_DAYS=0
set TOTAL_DAYS=0

echo Starting monthly backup process...
echo Processing days %START_DAY% to %END_DAY%...
echo.

REM Process each day
for /l %%d in (%START_DAY%,1,%END_DAY%) do (
    set /a TOTAL_DAYS+=1
    set DAY=%%d
    if !DAY! LSS 10 set DAY=0!DAY!

    echo ===============================================================
    echo Processing Day %%d of Month %MONTH_NUM% (%YEAR%-%MONTH_NUM%-!DAY!)
    echo ===============================================================

    REM Build the specific date
    set TARGET_DATE=%YEAR%-%MONTH_NUM%-!DAY!

    REM SECURITY FIX: Build and sanitize command parameters
    REM CRITICAL FIX: Add --date parameter to pass target date to daily backup
    set DAILY_PARAMS=--date !TARGET_DATE!
    if "%DRY_RUN%"=="true" set DAILY_PARAMS=!DAILY_PARAMS! --dry-run
    if "%VERBOSE%"=="true" set DAILY_PARAMS=!DAILY_PARAMS! --verbose

    REM CRITICAL FIX: Always pass date parameter - bypass sanitization for now
    REM Build final command with date parameter
    set FINAL_PARAMS=--date !TARGET_DATE!
    if "%DRY_RUN%"=="true" set FINAL_PARAMS=!FINAL_PARAMS! --dry-run
    if "%VERBOSE%"=="true" set FINAL_PARAMS=!FINAL_PARAMS! --verbose

    REM Run daily backup for this specific day with date parameter
    echo Running: run_daily_backup.bat !FINAL_PARAMS!
    echo Target date: !TARGET_DATE!
    echo.

    REM Execute with date parameter to ensure correct date processing
    call run_daily_backup.bat !FINAL_PARAMS!
    set DAY_EXIT_CODE=!ERRORLEVEL!

    if !DAY_EXIT_CODE! EQU 0 (
        echo ✅ Day %%d completed successfully
        set /a SUCCESSFUL_DAYS+=1
    ) else (
        echo ❌ Day %%d failed with exit code !DAY_EXIT_CODE!
        set /a FAILED_DAYS+=1
    )

    echo.
    echo Progress: !SUCCESSFUL_DAYS! successful, !FAILED_DAYS! failed out of !TOTAL_DAYS! processed
    echo.

    REM For dry-run, just process the first day to test
    if "%DRY_RUN%"=="true" (
        echo.
        echo DRY-RUN: Processing only first day for validation
        goto end_processing
    )

    REM Small delay between days
    timeout /t 2 /nobreak >nul
)

:end_processing

echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Total days processed: %TOTAL_DAYS%
echo Successful days: %SUCCESSFUL_DAYS%
echo Failed days: %FAILED_DAYS%
echo Completion time: %date% %time%
echo ===============================================================

if %FAILED_DAYS% EQU 0 (
    echo ✅ Monthly backup completed successfully!
    echo All days were processed without errors.
    exit /b 0
) else (
    echo ⚠️ Monthly backup completed with %FAILED_DAYS% failed days.
    echo Check individual day logs for details.
    exit /b 1
)

:invalid_month
echo ERROR: Invalid month '%MONTH_NAME%'. Must be 1-12 or month name.
exit /b 1

:historical_mode
echo ===============================================================
echo TNGD HISTORICAL BACKUP SYSTEM
echo ===============================================================
echo Start Date: %START_DATE%
echo End Date: %END_DATE%
echo Current time: %date% %time%
echo ===============================================================
echo.

REM SECURITY FIX: Enhanced date validation for historical mode
if "%START_DATE%"=="" (
    echo ERROR: Start date is required for historical mode
    goto help
)

if "%END_DATE%"=="" (
    echo ERROR: End date is required for historical mode
    goto help
)

REM SECURITY FIX: Validate date formats using TNGD validation patterns
python utils\batch_security_helper.py validate-date --date "%START_DATE%" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Invalid start date format: %START_DATE%
    echo Expected format: YYYY-MM-DD
    exit /b 1
)

python utils\batch_security_helper.py validate-date --date "%END_DATE%" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Invalid end date format: %END_DATE%
    echo Expected format: YYYY-MM-DD
    exit /b 1
)

REM Parse additional options for historical mode
shift
:parse_historical_options
if "%1"=="" goto historical_options_done
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_historical_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_historical_options
)
if "%1"=="--single-table" (
    set SINGLE_TABLE=true
    shift & goto parse_historical_options
)
shift & goto parse_historical_options

:historical_options_done

REM SECURITY FIX: Create logs directory using configured paths
if not exist "%LOG_BASE_DIR%" mkdir "%LOG_BASE_DIR%"
if not exist "%LOG_BASE_DIR%\historical" mkdir "%LOG_BASE_DIR%\historical"

REM SECURITY FIX: Create secure log file instead of predictable naming
for /f "tokens=*" %%f in ('python utils\batch_security_helper.py create-log-file --prefix historical_backup --suffix .log 2^>nul') do set "LOG_FILE=%%f"

REM Fallback to timestamped file if secure creation fails
if "%LOG_FILE%"=="" (
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YYYY=!dt:~0,4!"
    set "MM=!dt:~4,2!"
    set "DD=!dt:~6,2!"
    set "HH=!dt:~8,2!"
    set "Min=!dt:~10,2!"
    set "Sec=!dt:~12,2!"
    set "LOG_FILE=%LOG_BASE_DIR%\historical\historical_backup_!YYYY!!MM!!DD!_!HH!!Min!!Sec!.log"
)

if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
if "%SINGLE_TABLE%"=="true" echo Mode: SINGLE TABLE TEST
echo Log file: %LOG_FILE%
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM SECURITY FIX: Enhanced disk space check with better error handling
echo Checking disk space and performing cleanup...
echo [%date% %time%] Starting disk cleanup check >> "%LOG_FILE%"

%PYTHON_CMD% utils\disk_cleanup.py --force --verbose >> "%LOG_FILE%" 2>&1
set CLEANUP_EXIT_CODE=%ERRORLEVEL%

if %CLEANUP_EXIT_CODE% NEQ 0 (
    echo ERROR: Disk cleanup failed or insufficient disk space for historical backup.
    echo Check log file for details: %LOG_FILE%
    echo [%date% %time%] Disk cleanup failed with exit code %CLEANUP_EXIT_CODE% >> "%LOG_FILE%"
    exit /b 1
)

echo [%date% %time%] Disk cleanup completed successfully >> "%LOG_FILE%"

REM SECURITY FIX: Build and sanitize command parameters
set PARAMS=--start-date %START_DATE% --end-date %END_DATE%
if "%DRY_RUN%"=="true" set PARAMS=%PARAMS% --dry-run
if "%VERBOSE%"=="true" set PARAMS=%PARAMS% --verbose
if "%SINGLE_TABLE%"=="true" set PARAMS=%PARAMS% --single-table

REM SECURITY FIX: Sanitize parameters before execution
for /f "tokens=*" %%s in ('python utils\batch_security_helper.py sanitize-params %PARAMS% 2^>nul') do set SANITIZED_PARAMS=%%s

REM SECURITY FIX: Enhanced logging without exposing sensitive parameters
echo Starting historical backup process...
echo [%date% %time%] Historical backup started >> "%LOG_FILE%"
echo [%date% %time%] Start date: %START_DATE% >> "%LOG_FILE%"
echo [%date% %time%] End date: %END_DATE% >> "%LOG_FILE%"
if "%DRY_RUN%"=="true" echo [%date% %time%] Mode: DRY RUN >> "%LOG_FILE%"
echo.

REM SECURITY FIX: Execute with sanitized parameters
%PYTHON_CMD% %HISTORICAL_BACKUP_SCRIPT% %SANITIZED_PARAMS% >> "%LOG_FILE%" 2>&1
set BACKUP_EXIT_CODE=%ERRORLEVEL%

echo.
echo ===============================================================
echo HISTORICAL BACKUP COMPLETED at %date% %time%
echo Exit code: %BACKUP_EXIT_CODE%
echo ===============================================================

REM SECURITY FIX: Enhanced completion logging
echo [%date% %time%] Historical backup completed with exit code %BACKUP_EXIT_CODE% >> "%LOG_FILE%"

if %BACKUP_EXIT_CODE% EQU 0 (
    echo ✅ Historical backup completed successfully!
    echo [%date% %time%] Backup completed successfully >> "%LOG_FILE%"
) else (
    echo ❌ Historical backup failed or completed with errors.
    echo Check the log file for details: %LOG_FILE%
    echo [%date% %time%] Backup failed - check logs for details >> "%LOG_FILE%"
)

echo.
echo Log file: %LOG_FILE%
echo.

REM Show recent log entries for quick review
echo ===============================================================
echo RECENT LOG ENTRIES (Last 20 lines):
echo ===============================================================
if exist "%LOG_FILE%" (
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 20"
) else (
    echo Log file not found.
)
echo ===============================================================

exit /b %BACKUP_EXIT_CODE%

:help
echo ===============================================================
echo TNGD Monthly Backup System - Unified Help
echo ===============================================================
echo.
echo This is the unified monthly backup system that consolidates
echo monthly and historical backup functionality into a single interface.
echo.
echo Usage:
echo   run_monthly_backup.bat [mode] [parameters] [options]
echo.
echo MODES:
echo ===============================================================
echo.
echo 1. MONTHLY MODE (default)
echo   run_monthly_backup.bat [monthly] [month] [year] [options]
echo
echo   Parameters:
echo     month              Month name (january, february, march, etc.) or number (1-12)
echo     year               Year (e.g., 2025)
echo
echo   Options:
echo     --start-day DD     Start from specific day (default: 1)
echo     --end-day DD       End at specific day (default: last day of month)
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo.
echo   Examples:
echo     run_monthly_backup.bat march 2025
echo     run_monthly_backup.bat 3 2025
echo     run_monthly_backup.bat monthly march 2024 --dry-run
echo     run_monthly_backup.bat 3 2025 --start-day 15 --end-day 20
echo     run_monthly_backup.bat february 2024 --verbose
echo.
echo 2. HISTORICAL MODE
echo   run_monthly_backup.bat historical [start-date] [end-date] [options]
echo
echo   Parameters:
echo     start-date         Start date in YYYY-MM-DD format
echo     end-date           End date in YYYY-MM-DD format
echo
echo   Options:
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo     --single-table     Test with single table only
echo.
echo   Examples:
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo     run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
echo     run_monthly_backup.bat 2025-03-01 2025-03-31
echo.
echo ===============================================================
echo FEATURES:
echo ===============================================================
echo   ✅ Day-by-day sequential processing for maximum reliability
echo   ✅ Uses existing daily backup infrastructure
echo   ✅ Comprehensive error handling and retry mechanism
echo   ✅ Progress tracking and email notifications
echo   ✅ Resume capability from last successful day
echo   ✅ Multiple backup strategies available
echo   ✅ Historical date range backup support
echo   ✅ Automatic disk space management
echo   ✅ Configurable OSS path structure: Devo/{month}/week {n}/{date}/
echo.
echo ===============================================================
echo BACKUP STRATEGIES:
echo ===============================================================
echo 1. Day-by-Day Sequential (Recommended)
echo    - Maximum reliability and easy resumption
echo    - Each day processed independently
echo    - Minimal resource usage
echo.
echo 2. Week-by-Week Chunked
echo    - Balance between efficiency and reliability
echo    - Weekly data chunks
echo    - Faster than day-by-day
echo.
echo 3. Hybrid Adaptive
echo    - Adapts to table characteristics
echo    - Optimized chunk sizes
echo    - Best for diverse datasets
echo.
echo 4. Emergency Fallback
echo    - Maximum safety mode
echo    - Conservative settings with maximum retries
echo    - Used when other strategies fail
echo.
echo ===============================================================
echo QUICK START:
echo ===============================================================
echo 1. Test monthly backup:     run_monthly_backup.bat march 2025 --dry-run
echo 2. Run monthly backup:      run_monthly_backup.bat march 2025
echo 3. Test historical backup:  run_monthly_backup.bat historical 2025-03-01 2025-03-31 --dry-run
echo 4. Run historical backup:   run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo.
echo This script uses the existing daily backup system to process
echo data sequentially for maximum reliability. Each day/period is
echo processed as a separate backup operation with full error handling.
echo.
echo ===============================================================
exit /b 0
