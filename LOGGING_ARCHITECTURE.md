# TNGD Backup System - Enhanced Logging Architecture

## Overview

This document outlines the enhanced logging architecture for the TNGD backup system, designed to improve clarity, maintainability, traceability, and security.

## Architecture Components

### 1. Centralized Logging Manager (`utils/enhanced_logging.py`)

**Core Features:**
- Unified logging interface for all components
- Correlation ID generation and tracking
- Structured logging with consistent formats
- Configurable log levels and outputs
- Security-aware sensitive data filtering
- Performance-optimized logging

**Key Classes:**
- `EnhancedLogger`: Main logging interface
- `LogContext`: Context manager for correlation tracking
- `LogFormatter`: Standardized message formatting
- `SecurityFilter`: Sensitive data filtering
- `PerformanceLogger`: Performance-aware logging

### 2. Log Folder Structure

```
logs/
├── daily/
│   ├── 2025-06-23/
│   │   ├── backup_operations.log
│   │   ├── table_processing.log
│   │   ├── storage_operations.log
│   │   └── errors.log
│   └── archive/
├── monthly/
│   ├── 2025-06/
│   │   ├── backup_operations.log
│   │   ├── performance_summary.log
│   │   └── errors.log
│   └── archive/
├── historical/
│   ├── operations.log
│   └── archive/
├── performance/
│   ├── 2025-06-23/
│   │   ├── system_metrics.log
│   │   ├── table_performance.log
│   │   └── resource_usage.log
│   └── archive/
├── security/
│   ├── access.log
│   ├── audit.log
│   └── archive/
└── system/
    ├── startup.log
    ├── configuration.log
    ├── errors.log
    └── archive/
```

### 3. Log Levels and Usage

**CRITICAL**: System failures, data corruption, security breaches
- Use for: OSS connection failures, data integrity issues, authentication failures

**ERROR**: Operation failures that require attention
- Use for: Table backup failures, compression errors, validation failures

**WARNING**: Issues that don't stop operations but need monitoring
- Use for: Retry attempts, performance degradation, configuration issues

**INFO**: Normal operational information
- Use for: Backup start/completion, progress updates, configuration changes

**DEBUG**: Detailed diagnostic information
- Use for: Query details, chunk processing, internal state changes

### 4. Message Format Standards

**Structured Format (JSON-like for parsing):**
```json
{
  "timestamp": "2025-06-23T10:30:45.123456",
  "level": "INFO",
  "correlation_id": "backup_20250623_103045_abc123",
  "component": "table_processor",
  "operation": "backup_table",
  "phase": "PROCESS",
  "table_name": "my.app.tngd.waf",
  "message": "Processing chunk 1/5",
  "metadata": {
    "rows_processed": 50000,
    "chunk_size": 500000,
    "duration_ms": 1250
  }
}
```

**Human-Readable Format (Console/File):**
```
2025-06-23 10:30:45.123 [INFO] [backup_20250623_103045_abc123] table_processor.backup_table.PROCESS: Processing chunk 1/5 for my.app.tngd.waf (rows: 50000, duration: 1.25s)
```

### 5. Correlation ID Strategy

**Format**: `{operation_type}_{date}_{time}_{random_suffix}`
- Examples:
  - `daily_backup_20250623_103045_abc123`
  - `monthly_backup_20250623_140000_def456`
  - `single_table_20250623_151230_ghi789`

**Propagation**: Correlation IDs are passed through all related operations and logged consistently.

### 6. Security Features

**Sensitive Data Filtering:**
- Credentials (passwords, tokens, keys)
- Full file paths (show relative paths only)
- IP addresses (optional masking)
- User identifiers (optional hashing)

**Log Integrity:**
- Optional cryptographic signatures for audit logs
- Tamper detection for critical log files
- Secure file permissions (600 for log files, 700 for directories)

### 7. Performance Considerations

**Adaptive Logging:**
- Reduce verbosity during high-load operations
- Buffer log messages during critical sections
- Asynchronous logging for performance-critical paths

**Log Rotation:**
- Size-based rotation (configurable, default 50MB)
- Time-based rotation (daily for operational logs)
- Automatic compression of archived logs
- Configurable retention periods

## Configuration Schema

```json
{
  "logging": {
    "level": "INFO",
    "console_level": "INFO",
    "file_level": "DEBUG",
    "structured_format": true,
    "correlation_tracking": true,
    "security": {
      "filter_sensitive_data": true,
      "mask_file_paths": true,
      "enable_integrity_check": false
    },
    "performance": {
      "adaptive_logging": true,
      "async_logging": false,
      "buffer_size": 1000
    },
    "rotation": {
      "max_file_size_mb": 50,
      "max_files": 10,
      "compress_archived": true,
      "retention_days": 30
    },
    "outputs": {
      "console": true,
      "file": true,
      "structured_file": true,
      "syslog": false
    }
  }
}
```

## Migration Strategy

1. **Phase 1**: Implement new logging manager alongside existing system
2. **Phase 2**: Update core modules to use new logging interface
3. **Phase 3**: Migrate utility modules and scripts
4. **Phase 4**: Remove old logging utilities and clean up
5. **Phase 5**: Enable advanced features (security, performance optimizations)

## Benefits

- **Improved Debugging**: Correlation IDs and structured logging
- **Better Monitoring**: Standardized formats for log aggregation
- **Enhanced Security**: Sensitive data filtering and integrity checks
- **Operational Efficiency**: Clear log organization and automated rotation
- **Compliance Ready**: Audit trails and tamper detection
- **Performance Optimized**: Adaptive logging and async capabilities